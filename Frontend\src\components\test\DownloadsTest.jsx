import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchBuyerDownloads, selectMyDownloads, selectLoading, selectErrors } from '../../redux/slices/buyerDashboardSlice';

/**
 * Test component to verify downloads functionality
 * This component can be temporarily added to test the downloads system
 */
const DownloadsTest = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    // Test fetching downloads
    const testFetchDownloads = async () => {
      try {
        console.log('🧪 Testing downloads fetch...');
        await dispatch(fetchBuyerDownloads()).unwrap();
        setTestResults(prev => ({
          ...prev,
          fetch: { success: true, message: 'Downloads fetched successfully' }
        }));
      } catch (error) {
        setTestResults(prev => ({
          ...prev,
          fetch: { success: false, message: error.message }
        }));
      }
    };

    testFetchDownloads();
  }, [dispatch]);

  const runTests = () => {
    const results = {};

    // Test 1: Check if downloads array exists
    results.downloadsArray = {
      success: Array.isArray(downloads),
      message: Array.isArray(downloads) ? 'Downloads is an array' : 'Downloads is not an array'
    };

    // Test 2: Check downloads structure
    if (downloads.length > 0) {
      const firstDownload = downloads[0];
      const requiredFields = ['_id', 'title', 'coach', 'amount', 'fileType'];
      const hasRequiredFields = requiredFields.every(field => firstDownload.hasOwnProperty(field));
      
      results.structure = {
        success: hasRequiredFields,
        message: hasRequiredFields ? 'Download objects have required fields' : 'Missing required fields'
      };
    } else {
      results.structure = {
        success: true,
        message: 'No downloads to test structure (empty state is valid)'
      };
    }

    // Test 3: Check loading states
    results.loadingStates = {
      success: typeof loading.downloads === 'boolean',
      message: 'Loading state is properly typed'
    };

    // Test 4: Check error handling
    results.errorHandling = {
      success: errors.downloads === null || typeof errors.downloads === 'string',
      message: 'Error state is properly handled'
    };

    setTestResults(results);
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #007bff', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3>🧪 Downloads System Test</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>Current State:</h4>
        <p><strong>Downloads Count:</strong> {downloads.length}</p>
        <p><strong>Loading:</strong> {loading.downloads ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {errors.downloads || 'None'}</p>
      </div>

      <button 
        onClick={runTests}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginBottom: '20px'
        }}
      >
        Run Tests
      </button>

      {Object.keys(testResults).length > 0 && (
        <div>
          <h4>Test Results:</h4>
          {Object.entries(testResults).map(([testName, result]) => (
            <div 
              key={testName}
              style={{
                padding: '10px',
                margin: '5px 0',
                backgroundColor: result.success ? '#d4edda' : '#f8d7da',
                border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
                borderRadius: '4px'
              }}
            >
              <strong>{testName}:</strong> 
              <span style={{ color: result.success ? '#155724' : '#721c24' }}>
                {result.success ? ' ✅ PASS' : ' ❌ FAIL'}
              </span>
              <br />
              <small>{result.message}</small>
            </div>
          ))}
        </div>
      )}

      {downloads.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <h4>Sample Download Data:</h4>
          <pre style={{ 
            backgroundColor: '#e9ecef', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }}>
            {JSON.stringify(downloads[0], null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DownloadsTest;
