const express = require('express');
const { check } = require('express-validator');
const {
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  getBuyerOrders,
  getBuyerDownloads,
  getSellerOrders,
  downloadContent
} = require('../controllers/orders');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// User routes (accessible to all authenticated users)
router.get('/buyer', getBuyerOrders);
router.get('/buyer/downloads', getBuyerDownloads);
router.get('/:id/download', downloadContent);
router.get('/seller', getSellerOrders);

// Admin routes
router.get('/', authorize('admin'), getOrders);
router.put('/:id', authorize('admin'), updateOrder);

// Common routes
router.get('/:id', getOrder);

router.post(
  '/',
  authorize('buyer', 'admin'),
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('orderType', 'Order type is required').isIn(['Fixed', 'Auction', 'Custom'])
  ],
  createOrder
);

module.exports = router;
