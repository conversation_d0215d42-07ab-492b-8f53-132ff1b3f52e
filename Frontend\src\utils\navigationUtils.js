import { toast } from 'react-toastify';
import authService from '../services/authService';

/**
 * Navigation utility functions for handling authentication and role-based access
 */

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isUserAuthenticated = () => {
  return authService.isAuthenticated();
};

/**
 * Get current user data
 * @returns {Object|null} User object or null
 */
export const getCurrentUser = () => {
  return authService.getStoredUser();
};

/**
 * Get user role
 * @returns {string} User role or 'visitor'
 */
export const getUserRole = () => {
  const user = getCurrentUser();
  return user?.role || 'visitor';
};

/**
 * Check if user can access buyer routes (role restrictions removed)
 * @returns {boolean} True if user can access buyer routes
 */
export const canAccessBuyerRoutes = () => {
  return isUserAuthenticated(); // All authenticated users can access buyer routes
};

/**
 * Check if user can access seller routes (role restrictions removed)
 * @returns {boolean} True if user can access seller routes
 */
export const canAccessSellerRoutes = () => {
  return isUserAuthenticated(); // All authenticated users can access seller routes
};

/**
 * Handle Buy tab navigation with authentication (role restrictions removed)
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleBuyNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access buyer features');
    navigate('/auth');
    return false;
  }

  // All authenticated users can access buyer features
  navigate('/buyer/dashboard');
  return true;
};

/**
 * Handle Sell tab navigation with authentication (role restrictions removed)
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleSellNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access seller features');
    navigate('/auth');
    return false;
  }

  // All authenticated users can access seller features
  navigate('/seller/dashboard');
  return true;
};

/**
 * Get appropriate dashboard route for user role
 * @returns {string} Dashboard route path
 */
export const getUserDashboardRoute = () => {
  const userRole = getUserRole();

  switch (userRole) {
    case 'buyer':
      return '/buyer/account/dashboard';
    case 'seller':
      return '/seller/dashboard';
    case 'admin':
      return '/admin/dashboard';
    default:
      return '/';
  }
};

/**
 * Check if current route matches user's role (role restrictions removed - always return true for authenticated users)
 * @param {string} pathname - Current route pathname
 * @returns {boolean} True if route is appropriate for user role
 */
export const isRouteAuthorizedForUser = (pathname) => {
  if (!isUserAuthenticated()) {
    return true; // Allow public routes for non-authenticated users
  }

  // All authenticated users can access all routes (role restrictions removed)
  return true;
};

/**
 * Handle unauthorized route access (simplified since role restrictions are removed)
 * @param {string} pathname - Attempted route pathname
 * @param {Function} navigate - React Router navigate function
 */
export const handleUnauthorizedAccess = (pathname, navigate) => {
  // Since role restrictions are removed, this function is simplified
  // Only redirect if user is not authenticated
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access this feature');
    navigate('/auth');
  }
};
