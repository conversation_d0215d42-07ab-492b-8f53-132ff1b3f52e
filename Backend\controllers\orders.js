const ErrorResponse = require('../utils/errorResponse');
const Order = require('../models/Order');
const Content = require('../models/Content');
const User = require('../models/User');
const Payment = require('../models/Payment');
const Bid = require('../models/Bid');
const CustomRequest = require('../models/CustomRequest');
const { validationResult } = require('express-validator');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

// @desc    Get all orders
// @route   GET /api/orders
// @access  Private/Admin
exports.getOrders = async (req, res, next) => {
  try {
    const orders = await Order.find()
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      })
      .populate('content');

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
exports.getOrder = async (req, res, next) => {
  try {
    const orderId = req.params.id;

    // Validate order ID format
    if (!orderId || orderId === 'undefined' || orderId.trim() === '') {
      return next(
        new ErrorResponse('Order ID is required', 400)
      );
    }

    // Check if it's a valid MongoDB ObjectId
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      return next(
        new ErrorResponse('Invalid order ID format', 400)
      );
    }

    const order = await Order.findById(orderId)
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      })
      .populate('content');

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order owner or seller or admin
    if (
      order.buyer._id.toString() !== req.user.id &&
      order.seller._id.toString() !== req.user.id &&
      req.user.role !== 'admin'
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this order`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new order
// @route   POST /api/orders
// @access  Private/Buyer
exports.createOrder = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { contentId, orderType, bidId, customRequestId } = req.body;

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole = req.user.role === 'admin' ? req.user.role : req.user.activeRole;
    if (effectiveRole !== 'buyer' && req.user.role !== 'admin') {
      return next(
        new ErrorResponse(`User ${req.user.id} is not authorized to create an order`, 403)
      );
    }

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${contentId}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== 'Published') {
      return next(
        new ErrorResponse(`Content is not available for purchase`, 400)
      );
    }

    // Get seller
    const seller = await User.findById(content.seller);
    if (!seller) {
      return next(
        new ErrorResponse(`Seller not found with id of ${content.seller}`, 404)
      );
    }

    // Calculate amount based on order type
    let amount = 0;

    if (orderType === 'Fixed') {
      // Fixed price order
      amount = content.price;
    } else if (orderType === 'Auction') {
      // Auction order
      if (!bidId) {
        return next(
          new ErrorResponse(`Bid ID is required for auction orders`, 400)
        );
      }

      const bid = await Bid.findById(bidId);
      if (!bid) {
        return next(
          new ErrorResponse(`Bid not found with id of ${bidId}`, 404)
        );
      }

      // Check if bid belongs to the user
      if (bid.bidder.toString() !== req.user.id) {
        return next(
          new ErrorResponse(`User is not authorized to use this bid`, 403)
        );
      }

      // Check if bid is for the correct content
      if (bid.content.toString() !== contentId) {
        return next(
          new ErrorResponse(`Bid is not for the specified content`, 400)
        );
      }

      // Check if bid is active
      if (bid.status !== 'Won') {
        return next(
          new ErrorResponse(`Bid is not in a winning state`, 400)
        );
      }

      amount = bid.amount;
    } else if (orderType === 'Custom') {
      // Custom request order
      if (!customRequestId) {
        return next(
          new ErrorResponse(`Custom request ID is required for custom orders`, 400)
        );
      }

      const customRequest = await CustomRequest.findById(customRequestId);
      if (!customRequest) {
        return next(
          new ErrorResponse(`Custom request not found with id of ${customRequestId}`, 404)
        );
      }

      // Check if custom request belongs to the user
      if (customRequest.buyer.toString() !== req.user.id) {
        return next(
          new ErrorResponse(`User is not authorized to use this custom request`, 403)
        );
      }

      // Check if custom request is accepted
      if (customRequest.status !== 'Accepted') {
        return next(
          new ErrorResponse(`Custom request is not in an accepted state`, 400)
        );
      }

      amount = customRequest.sellerResponse.price;
    } else {
      return next(
        new ErrorResponse(`Invalid order type`, 400)
      );
    }

    // Calculate platform fee (get from settings)
    const platformFeePercentage = process.env.PLATFORM_COMMISSION || 10;
    const platformFee = (amount * platformFeePercentage) / 100;
    const sellerEarnings = amount - platformFee;

    // Create order
    const order = await Order.create({
      buyer: req.user.id,
      seller: content.seller,
      content: contentId,
      orderType,
      amount,
      platformFee,
      sellerEarnings,
      bidId,
      customRequestId,
      status: 'Pending',
      paymentStatus: 'Pending'
    });

    // Generate invoice
    const invoiceUrl = await generateInvoice(order);

    // Update order with invoice URL
    order.invoiceUrl = invoiceUrl;
    await order.save();

    // If custom request, update its status
    if (customRequestId) {
      await CustomRequest.findByIdAndUpdate(customRequestId, {
        status: 'Completed',
        orderId: order._id
      });
    }

    res.status(201).json({
      success: true,
      data: order
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update order status
// @route   PUT /api/orders/:id
// @access  Private/Admin
exports.updateOrder = async (req, res, next) => {
  try {
    const { status, paymentStatus } = req.body;

    let order = await Order.findById(req.params.id);

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${req.params.id}`, 404)
      );
    }

    // Only admin can update order status
    if (req.user.role !== 'admin') {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this order`,
          403
        )
      );
    }

    // Update fields
    if (status) order.status = status;
    if (paymentStatus) order.paymentStatus = paymentStatus;

    await order.save();

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get buyer orders
// @route   GET /api/orders/buyer
// @access  Private/Buyer
exports.getBuyerOrders = async (req, res, next) => {
  try {
    const orders = await Order.find({ buyer: req.user.id })
      .populate({
        path: 'seller',
        select: 'firstName lastName email'
      })
      .populate('content')
      .sort('-createdAt');

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller orders
// @route   GET /api/orders/seller
// @access  Private/Seller
exports.getSellerOrders = async (req, res, next) => {
  try {
    const orders = await Order.find({ seller: req.user.id })
      .populate({
        path: 'buyer',
        select: 'firstName lastName email'
      })
      .populate('content')
      .sort('-createdAt');

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Download content
// @route   GET /api/orders/:id/download
// @access  Private/Buyer
exports.downloadContent = async (req, res, next) => {
  try {
    console.log(`[Download] Starting download for order: ${req.params.id}`);
    console.log(`[Download] User: ${req.user.id}, Role: ${req.user.role}`);

    const order = await Order.findById(req.params.id).populate('content');

    if (!order) {
      console.log(`[Download] Order not found: ${req.params.id}`);
      return next(
        new ErrorResponse(`Order not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is order owner OR seller (both can download)
    const isOwner = order.buyer.toString() === req.user.id;
    const isSeller = order.seller.toString() === req.user.id;
    const isAdmin = req.user.role === 'admin';

    console.log(`[Download] Access check - Owner: ${isOwner}, Seller: ${isSeller}, Admin: ${isAdmin}`);
    console.log(`[Download] Order buyer: ${order.buyer}, Order seller: ${order.seller}`);

    if (!isOwner && !isSeller && !isAdmin) {
      console.log(`[Download] Access denied for user: ${req.user.id}`);
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to download this content`,
          403
        )
      );
    }

    // Check if payment is completed (only for buyers, sellers can always download)
    if (isOwner && order.paymentStatus !== 'Completed') {
      console.log(`[Download] Payment not completed. Status: ${order.paymentStatus}`);
      return next(
        new ErrorResponse(`Payment must be completed to download content`, 400)
      );
    }

    console.log(`[Download] Access granted. Payment status: ${order.paymentStatus}`);

    // Update download count (only for buyers)
    if (isOwner) {
      order.downloadCount = (order.downloadCount || 0) + 1;
      order.lastDownloaded = Date.now();
      await order.save();
      console.log(`[Download] Updated download count to: ${order.downloadCount}`);
    }

    const fileUrl = order.content.fileUrl;
    const fs = require('fs');
    const path = require('path');

    // Check if it's an S3 URL or local file
    if (fileUrl.includes('amazonaws.com') || fileUrl.includes('s3.')) {
      // For S3 files, return the direct URL
      res.status(200).json({
        success: true,
        data: {
          downloadUrl: fileUrl,
          fileName: order.content.title,
          fileType: order.content.contentType,
          isS3: true
        }
      });
    } else {
      // For local files, stream the file directly
      const filePath = path.join(__dirname, '..', fileUrl);

      console.log(`[Download] Attempting to download file: ${filePath}`);
      console.log(`[Download] File URL from DB: ${fileUrl}`);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        console.error(`[Download] File not found: ${filePath}`);
        return next(
          new ErrorResponse(`File not found: ${fileUrl}`, 404)
        );
      }

      // Get file stats
      const stat = fs.statSync(filePath);
      const fileSize = stat.size;

      console.log(`[Download] File size: ${fileSize} bytes`);

      // Set appropriate headers for file download
      const fileName = path.basename(filePath);
      const fileExtension = path.extname(fileName).toLowerCase();

      // Set content type based on file extension
      let contentType = 'application/octet-stream';
      if (fileExtension === '.pdf') contentType = 'application/pdf';
      else if (fileExtension === '.mp4') contentType = 'video/mp4';
      else if (fileExtension === '.mp3') contentType = 'audio/mpeg';
      else if (fileExtension === '.jpg' || fileExtension === '.jpeg') contentType = 'image/jpeg';
      else if (fileExtension === '.png') contentType = 'image/png';

      // Prevent caching and force download
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, private');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('ETag', ''); // Remove ETag to prevent 304 responses
      res.setHeader('Last-Modified', ''); // Remove Last-Modified to prevent 304 responses

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Length', fileSize);
      res.setHeader('Content-Disposition', `attachment; filename="${order.content.title}${fileExtension}"`);
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition, Content-Length, Content-Type');

      console.log(`[Download] Streaming file with content type: ${contentType}`);

      // Stream the file
      const fileStream = fs.createReadStream(filePath);

      fileStream.on('open', () => {
        console.log(`[Download] File stream opened successfully`);
      });

      fileStream.on('error', (error) => {
        console.error('[Download] File stream error:', error);
        if (!res.headersSent) {
          return next(new ErrorResponse('Error streaming file', 500));
        }
      });

      fileStream.on('end', () => {
        console.log(`[Download] File stream completed`);
      });

      fileStream.pipe(res);
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Stream content file for download
// @route   GET /api/orders/:id/stream
// @access  Private/Buyer
exports.streamContent = async (req, res, next) => {
  const fs = require('fs');
  const path = require('path');
  const AWS = require('aws-sdk');

  try {
    const order = await Order.findById(req.params.id).populate('content');

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is order owner
    if (order.buyer.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to download this content`,
          403
        )
      );
    }

    // Check if payment is completed
    if (order.paymentStatus !== 'Completed') {
      return next(
        new ErrorResponse(`Payment must be completed to download content`, 400)
      );
    }

    // Validate content and file URL
    if (!order.content || !order.content.fileUrl) {
      return next(
        new ErrorResponse(`Content file not available for download`, 404)
      );
    }

    const fileUrl = order.content.fileUrl;
    const fileName = order.content.title;
    const contentType = order.content.contentType;

    // Update download count
    order.downloadCount += 1;
    order.lastDownloaded = Date.now();
    await order.save();

    console.log('Streaming file for order:', order._id);
    console.log('File URL:', fileUrl);

    // Handle different file storage types
    if (fileUrl.startsWith('http')) {
      // External URL (S3, etc.) - redirect to the URL
      res.redirect(fileUrl);
    } else {
      // Local file - stream it
      const filePath = path.join(__dirname, '..', fileUrl.startsWith('/') ? fileUrl.substring(1) : fileUrl);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return next(
          new ErrorResponse(`File not found on server`, 404)
        );
      }

      // Get file stats
      const stat = fs.statSync(filePath);
      const fileSize = stat.size;

      // Set appropriate headers
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Length', fileSize);
      res.setHeader('Cache-Control', 'no-cache');

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      fileStream.on('error', (error) => {
        console.error('File stream error:', error);
        if (!res.headersSent) {
          return next(new ErrorResponse('Error streaming file', 500));
        }
      });
    }
  } catch (err) {
    console.error('Stream error:', err);
    next(err);
  }
};

// Helper function to generate invoice
const generateInvoice = async (order) => {
  try {
    // Create a new PDF document
    const doc = new PDFDocument();
    const invoiceFileName = `invoice-${order._id}.pdf`;
    const invoicePath = path.join('uploads', invoiceFileName);

    // Pipe the PDF to a file
    doc.pipe(fs.createWriteStream(invoicePath));

    // Add content to the PDF
    doc.fontSize(25).text('Invoice', { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(`Order ID: ${order._id}`);
    doc.text(`Date: ${new Date(order.createdAt).toLocaleDateString()}`);
    doc.moveDown();
    doc.text(`Buyer ID: ${order.buyer}`);
    doc.text(`Seller ID: ${order.seller}`);
    doc.moveDown();
    doc.text(`Content ID: ${order.content}`);
    doc.text(`Order Type: ${order.orderType}`);
    doc.moveDown();
    doc.text(`Amount: $${order.amount.toFixed(2)}`);
    doc.text(`Platform Fee: $${order.platformFee.toFixed(2)}`);
    doc.text(`Seller Earnings: $${order.sellerEarnings.toFixed(2)}`);
    doc.moveDown();
    doc.text(`Payment Status: ${order.paymentStatus}`);
    doc.text(`Order Status: ${order.status}`);

    // Finalize the PDF
    doc.end();

    // Return the invoice URL
    return `${process.env.NODE_ENV === 'production' ? 'https://' : 'http://'}localhost:${process.env.PORT}/uploads/${invoiceFileName}`;
  } catch (error) {
    console.error('Error generating invoice:', error);
    return null;
  }
};
