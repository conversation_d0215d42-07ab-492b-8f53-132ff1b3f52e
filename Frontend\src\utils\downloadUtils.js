/**
 * Download utilities for handling file downloads
 */

/**
 * Download a file from a URL with multiple fallback methods
 * @param {string} url - The file URL to download
 * @param {string} filename - The desired filename
 * @param {Object} options - Additional options
 */
export const downloadFileFromUrl = async (url, filename, options = {}) => {
  try {
    // If we have authentication headers, use blob download first
    if (options.headers && options.headers.Authorization) {
      const blobSuccess = await tryBlobDownload(url, filename, options);
      if (blobSuccess) return true;
    }

    // Method 1: Direct download link (works for most cases)
    const success = await tryDirectDownload(url, filename);
    if (success) return true;

    // Method 2: Fetch and blob download (handles CORS and authentication)
    const blobSuccess = await tryBlobDownload(url, filename, options);
    if (blobSuccess) return true;

    // Method 3: Open in new tab as fallback
    window.open(url, '_blank');
    return true;

  } catch (error) {
    console.error('All download methods failed:', error);
    throw new Error('Failed to download file');
  }
};

/**
 * Try direct download using anchor tag
 * @param {string} url - File URL
 * @param {string} filename - Filename
 */
const tryDirectDownload = async (url, filename) => {
  try {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // Add to DOM temporarily
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    return true;
  } catch (error) {
    console.log('Direct download failed:', error);
    return false;
  }
};

/**
 * Try blob download using fetch
 * @param {string} url - File URL
 * @param {string} filename - Filename
 * @param {Object} options - Additional options
 */
const tryBlobDownload = async (url, filename, options = {}) => {
  try {
    const headers = {
      ...options.headers
    };

    // Add authorization if available
    const token = localStorage.getItem('xosportshub_token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
      ...options.fetchOptions
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Check if response is JSON (error response) or actual file
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Download failed');
    }

    const blob = await response.blob();
    const blobUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the blob URL
    window.URL.revokeObjectURL(blobUrl);

    return true;
  } catch (error) {
    console.log('Blob download failed:', error);
    return false;
  }
};

/**
 * Get file extension based on file type
 * @param {string} fileType - The file type
 */
export const getFileExtension = (fileType) => {
  const extensions = {
    video: 'mp4',
    audio: 'mp3',
    image: 'jpg',
    pdf: 'pdf',
    document: 'pdf',
    text: 'txt',
    zip: 'zip',
    rar: 'rar'
  };

  return extensions[fileType?.toLowerCase()] || 'file';
};

/**
 * Generate filename from URL and content info
 * @param {string} url - File URL
 * @param {Object} content - Content object with title and fileType
 */
export const generateFilename = (url, content) => {
  // Try to extract filename from URL
  const urlParts = url.split('/');
  const urlFilename = urlParts[urlParts.length - 1];

  // If URL has a proper filename with extension, use it
  if (urlFilename && urlFilename.includes('.')) {
    return urlFilename;
  }

  // Otherwise, generate filename from content info
  const title = content.title || 'download';
  const extension = getFileExtension(content.fileType);

  // Clean title for filename (remove special characters)
  const cleanTitle = title.replace(/[^a-zA-Z0-9\s-_]/g, '').replace(/\s+/g, '_');

  return `${cleanTitle}.${extension}`;
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 */
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return 'Unknown size';

  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  if (i === 0) return `${bytes} ${sizes[i]}`;

  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

/**
 * Check if URL is downloadable (not a streaming URL)
 * @param {string} url - File URL
 */
export const isDownloadableUrl = (url) => {
  if (!url) return false;

  // Check for streaming URLs that shouldn't be downloaded directly
  const streamingPatterns = [
    /youtube\.com/,
    /youtu\.be/,
    /vimeo\.com/,
    /twitch\.tv/,
    /stream/,
    /hls/,
    /dash/
  ];

  return !streamingPatterns.some(pattern => pattern.test(url));
};

/**
 * Validate file type for download
 * @param {string} fileType - File type
 */
export const isValidFileType = (fileType) => {
  const validTypes = [
    'video', 'audio', 'image', 'pdf', 'document',
    'text', 'zip', 'rar', 'application'
  ];

  return validTypes.includes(fileType?.toLowerCase());
};

export default {
  downloadFileFromUrl,
  getFileExtension,
  generateFilename,
  formatFileSize,
  isDownloadableUrl,
  isValidFileType
};
