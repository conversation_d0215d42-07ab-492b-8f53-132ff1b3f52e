# XO Sports Hub - Digital Sports Training Marketplace

A comprehensive full-stack platform for buying and selling digital sports training content including videos, PDFs, and strategic materials.

## 🏆 Features

### 🔐 Authentication & User Management
- **Multi-role System**: Admin, Seller, Buyer roles with specific permissions
- **JWT Authentication**: Secure token-based authentication
- **Profile Management**: Complete user profiles with verification system
- **Email Verification**: Secure account verification process

### 💰 E-commerce & Payments
- **Stripe Integration**: Complete payment processing with webhooks
- **Order Management**: Full order lifecycle with status tracking
- **Download System**: Secure file downloads with access control
- **Invoice Generation**: Automated PDF invoice creation
- **Seller Payouts**: Automated commission and payout system

### 📱 Content Management
- **Multi-format Support**: Videos (MP4, AVI, MOV), PDFs, Images, Audio
- **Enhanced Video Player**: Custom controls with speed adjustment (0.25x-2x)
- **Preview System**: Video thumbnails and PDF previews
- **File Upload**: Secure file handling with validation
- **Content Categories**: Organized by sport, difficulty, and type

### 🎯 Advanced Features
- **Bidding System**: Auction-style content sales
- **Custom Requests**: Buyers can request specific content
- **Wishlist**: Save favorite content for later
- **Reviews & Ratings**: Community feedback system
- **Notifications**: Real-time in-app and email notifications
- **Search & Filters**: Advanced content discovery

### 🛡️ Security & Performance
- **CORS Protection**: Secure cross-origin requests
- **File Validation**: Comprehensive upload security
- **Rate Limiting**: API protection against abuse
- **Error Handling**: Comprehensive error management
- **Responsive Design**: Mobile-first UI/UX

## 🚀 Tech Stack

### Backend
- **Node.js & Express.js**: Server framework
- **MongoDB & Mongoose**: Database and ODM
- **JWT**: Authentication tokens
- **Stripe**: Payment processing
- **Multer**: File upload handling
- **Nodemailer**: Email notifications
- **PDFKit**: Invoice generation
- **FFmpeg**: Video processing
- **Helmet & CORS**: Security middleware

### Frontend
- **React 18**: Modern React with hooks
- **Redux Toolkit**: State management
- **React Router**: Client-side routing
- **Vite**: Fast build tool and dev server
- **CSS3**: Custom styling with responsive design
- **React Toastify**: User notifications

## 📦 Installation & Setup

### Prerequisites
- Node.js (v16+)
- MongoDB (v5+)
- Stripe Account
- SMTP Email Service

### 1. Clone Repository
```bash
git clone <repository-url>
cd xosportshub
```

### 2. Backend Setup
```bash
cd Backend
npm install
```

Create `.env` file in Backend directory:
```env
NODE_ENV=development
PORT=5000
MONGO_URI=mongodb://localhost:27017/xosportshub
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=XO Sports Hub

# File Upload
MAX_FILE_UPLOAD=********
FILE_UPLOAD_PATH=./uploads
```

### 3. Frontend Setup
```bash
cd Frontend
npm install
```

Create `.env` file in Frontend directory:
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_IMAGE_BASE_URL=http://localhost:5000

# App Configuration
VITE_APP_NAME=XO Sports Hub
VITE_APP_VERSION=1.0.0

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 4. Start Development Servers

**Backend:**
```bash
cd Backend
npm start
```

**Frontend:**
```bash
cd Frontend
npm run dev
```

### 5. Access Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/health

## 🎮 Usage Guide

### For Buyers
1. **Register/Login** with buyer role
2. **Browse Content** by categories and filters
3. **Preview Content** with enhanced video player
4. **Purchase Content** using Stripe payment
5. **Download Files** from "My Downloads" section
6. **Leave Reviews** and manage wishlist

### For Sellers
1. **Register/Login** with seller role
2. **Upload Content** with descriptions and pricing
3. **Manage Orders** and track sales
4. **View Analytics** and earnings
5. **Respond to Requests** from buyers
6. **Manage Bids** on auction content

### For Admins
1. **User Management** and verification
2. **Content Moderation** and approval
3. **Platform Settings** configuration
4. **Analytics Dashboard** monitoring
5. **Payment Management** and disputes

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/forgot-password` - Password reset

### Content
- `GET /api/content` - Browse content with filters
- `POST /api/content` - Upload new content (sellers)
- `GET /api/content/:id` - Get content details
- `PUT /api/content/:id` - Update content (sellers)

### Orders & Downloads
- `POST /api/orders` - Create new order
- `GET /api/orders/buyer/downloads` - Get buyer downloads
- `GET /api/orders/:id/download` - Download purchased content
- `GET /api/orders/seller` - Get seller orders

### Payments
- `POST /api/payments/create-payment-intent` - Create Stripe payment
- `POST /api/payments/webhook` - Stripe webhook handler
- `GET /api/payments/seller/balance` - Get seller balance

## 🎬 Enhanced Video Player Features

- **Custom Controls**: Play/pause, progress bar, volume control
- **Speed Control**: 0.25x, 0.5x, 0.75x, 1x, 1.25x, 1.5x, 1.75x, 2x
- **Fullscreen Support**: Native fullscreen functionality
- **Keyboard Shortcuts**: Space for play/pause, arrow keys for seeking
- **Mobile Responsive**: Touch-friendly controls
- **Time Display**: Current time / total duration
- **Hover Controls**: Controls appear on mouse hover

## 📱 Mobile Responsiveness

- **Responsive Design**: Works on all device sizes
- **Touch Optimized**: Mobile-friendly interactions
- **Progressive Web App**: PWA capabilities
- **Offline Support**: Basic offline functionality

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **CORS Protection**: Cross-origin request security
- **File Validation**: Upload security and type checking
- **Rate Limiting**: API abuse prevention
- **Input Sanitization**: XSS and injection protection
- **Secure Headers**: Helmet.js security headers

## 🚀 Deployment

### Production Environment Variables
Update `.env` files with production values:
- Database connection strings
- Stripe live keys
- Production email service
- Secure JWT secrets
- HTTPS URLs

### Build Commands
```bash
# Frontend build
cd Frontend && npm run build

# Backend production
cd Backend && npm start
```

## 📊 Performance Optimizations

- **File Streaming**: Efficient large file downloads
- **Image Optimization**: Compressed thumbnails
- **Lazy Loading**: Content loaded on demand
- **Caching**: Strategic API response caching
- **CDN Ready**: Static asset optimization

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact: <EMAIL>
- Documentation: [Project Wiki]

## 🎯 Roadmap

- [ ] Mobile App (React Native)
- [ ] Live Streaming Integration
- [ ] AI-Powered Recommendations
- [ ] Multi-language Support
- [ ] Advanced Analytics Dashboard
- [ ] Social Features & Community

---

**Built with ❤️ for the sports training community**
